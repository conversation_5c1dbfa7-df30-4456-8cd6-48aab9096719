import { Download, FileText, Eye } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const Resume = () => {
  const { language } = useLanguage();

  const translations = {
    en: {
      title: 'Resume',
      subtitle: 'Download my professional resume',
      description: 'Get a comprehensive overview of my experience, skills, and achievements in DevOps and VOIP technologies.',
      downloadBtn: 'Download Resume',
      viewBtn: 'View Online',
      lastUpdated: 'Last updated'
    },
    fr: {
      title: 'CV',
      subtitle: 'Téléchargez mon CV professionnel',
      description: 'Obtenez un aperçu complet de mon expérience, mes compétences et mes réalisations en DevOps et technologies VOIP.',
      downloadBtn: 'Télécharger le CV',
      viewBtn: 'Voir en ligne',
      lastUpdated: 'Dernière mise à jour'
    }
  };

  const resumeFile = language === 'fr' ? '/FR_CV_Alan_Ju<PERSON>court.pdf' : '/ENG_CV_<PERSON>_<PERSON>.pdf';
  const resumeFileName = language === 'fr' ? 'CV_<PERSON>_Jumeaucourt_FR.pdf' : 'CV_Alan_Ju<PERSON>aucourt_EN.pdf';

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = resumeFile;
    link.download = resumeFileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleView = () => {
    window.open(resumeFile, '_blank');
  };

  return (
    <section id="resume" className="py-20">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-400">
            {translations[language].title}
          </h2>
          <p className="text-xl text-gray-400 mb-2">
            {translations[language].subtitle}
          </p>
          <p className="text-gray-500 max-w-2xl mx-auto">
            {translations[language].description}
          </p>
        </div>

        <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-8 border border-gray-700 animate-slide-up">
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div className="flex items-center space-x-4">
              <div className="bg-blue-500/20 p-4 rounded-lg animate-pulse-glow">
                <FileText className="w-8 h-8 text-blue-400" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-white mb-1">
                  Alan Jumeaucourt - {translations[language].title}
                </h3>
                <p className="text-gray-400">
                  {translations[language].lastUpdated}: {new Date().toLocaleDateString(language === 'fr' ? 'fr-FR' : 'en-US')}
                </p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={handleView}
                className="flex items-center space-x-2 bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                <Eye className="w-5 h-5" />
                <span>{translations[language].viewBtn}</span>
              </button>
              <button
                onClick={handleDownload}
                className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-all duration-300 transform hover:scale-105"
              >
                <Download className="w-5 h-5" />
                <span>{translations[language].downloadBtn}</span>
              </button>
            </div>
          </div>

          {/* Preview section */}
          <div className="mt-8 border-t border-gray-700 pt-8">
            <div className="bg-gray-900/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-white">Preview</h4>
                <span className="text-sm text-gray-400">PDF Document</span>
              </div>
              <div className="aspect-[3/4] bg-gray-800 rounded-lg border border-gray-600 overflow-hidden relative">
                <iframe
                  src={`${resumeFile}#toolbar=0&navpanes=0&scrollbar=0`}
                  className="w-full h-full"
                  title={`${translations[language].title} - Alan Jumeaucourt`}
                  onError={() => {
                    // Fallback: open in new tab if iframe fails
                    window.open(resumeFile, '_blank');
                  }}
                />
                <div className="absolute inset-0 bg-gray-800/20 pointer-events-none rounded-lg" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Resume;
