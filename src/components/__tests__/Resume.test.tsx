import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import Resume from '../Resume';
import { LanguageProvider } from '../../contexts/LanguageContext';

// Mock the useLanguage hook
const mockSetLanguage = vi.fn();
vi.mock('../../contexts/LanguageContext', () => ({
  LanguageProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useLanguage: () => ({
    language: 'en',
    setLanguage: mockSetLanguage,
  }),
}));

// Mock window.open
Object.defineProperty(window, 'open', {
  writable: true,
  value: vi.fn(),
});

describe('Resume Component', () => {
  it('renders resume section with correct title', () => {
    render(
      <LanguageProvider>
        <Resume />
      </LanguageProvider>
    );

    expect(screen.getByText('Resume')).toBeInTheDocument();
    expect(screen.getByText('Download my professional resume')).toBeInTheDocument();
  });

  it('has download and view buttons', () => {
    render(
      <LanguageProvider>
        <Resume />
      </LanguageProvider>
    );

    expect(screen.getByText('Download Resume')).toBeInTheDocument();
    expect(screen.getByText('View Online')).toBeInTheDocument();
  });

  it('opens PDF in new tab when view button is clicked', () => {
    render(
      <LanguageProvider>
        <Resume />
      </LanguageProvider>
    );

    const viewButton = screen.getByText('View Online');
    fireEvent.click(viewButton);

    expect(window.open).toHaveBeenCalledWith('/ENG_CV_Alan_Jumeaucourt.pdf', '_blank');
  });

  it('triggers download when download button is clicked', () => {
    // Mock document.createElement and appendChild/removeChild
    const mockLink = {
      href: '',
      download: '',
      click: vi.fn(),
    };
    const mockAppendChild = vi.fn();
    const mockRemoveChild = vi.fn();

    vi.spyOn(document, 'createElement').mockReturnValue(mockLink as any);
    vi.spyOn(document.body, 'appendChild').mockImplementation(mockAppendChild);
    vi.spyOn(document.body, 'removeChild').mockImplementation(mockRemoveChild);

    render(
      <LanguageProvider>
        <Resume />
      </LanguageProvider>
    );

    const downloadButton = screen.getByText('Download Resume');
    fireEvent.click(downloadButton);

    expect(document.createElement).toHaveBeenCalledWith('a');
    expect(mockLink.href).toBe('/ENG_CV_Alan_Jumeaucourt.pdf');
    expect(mockLink.download).toBe('CV_Alan_Jumeaucourt_EN.pdf');
    expect(mockLink.click).toHaveBeenCalled();
    expect(mockAppendChild).toHaveBeenCalledWith(mockLink);
    expect(mockRemoveChild).toHaveBeenCalledWith(mockLink);
  });
});
