@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-900 text-gray-100;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .card {
    @apply bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-xl;
  }

  .section-fade {
    @apply opacity-0 translate-y-8 transition-all duration-700 ease-out;
  }

  .section-fade.in-view {
    @apply opacity-100 translate-y-0;
  }
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes ping-slow {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping-slow {
  animation: ping-slow 2s cubic-bezier(0, 0, 0.2, 1) infinite;
}
